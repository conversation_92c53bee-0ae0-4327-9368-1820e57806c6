<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="76b702cc-2850-472c-9039-935eb1ffa66c" name="Changes" comment="fix:获取到DEFAULT的联系人">
      <change afterPath="$PROJECT_DIR$/.idea/shelf/Uncommitted_changes_before_Update_at_2025_7_25_19_12_[Changes]/shelved.patch" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/shelf/Uncommitted_changes_before_Update_at_2025_7_25_19_12__Changes_.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/shelf/Uncommitted_changes_before_Update_at_2025_7_28_17_23_[Changes]/shelved.patch" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/shelf/Uncommitted_changes_before_Update_at_2025_7_28_17_23__Changes_.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/shelf/Uncommitted_changes_before_Update_at_2025_7_29_11_07_[Changes]/shelved.patch" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/shelf/Uncommitted_changes_before_Update_at_2025_7_29_11_07__Changes_.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/uiDesigner.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/docs/需求管理整合对接记录功能说明.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/ruoyi-miniapp/target/classes/mapper/miniapp/MiniContactMapper.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/sql/tjuhaitang_miniapp_db.sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/小程序端页面展示示例.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/页面内容管理功能测试.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/页面内容管理菜单权限.sql" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.augment/rules/规则.md" beforeDir="false" afterPath="$PROJECT_DIR$/.augment/rules/规则.md" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.gitignore" beforeDir="false" afterPath="$PROJECT_DIR$/.gitignore" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/AugmentWebviewStateStore.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/shelf/Uncommitted_changes_before_Update_at_2025_7_7_15_43__Changes_.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/workspace.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/workspace.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/docs/mini_notice_enhancement_summary.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/docs/miniapp_auth_api.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/docs/miniapp_auth_implementation_summary.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/docs/miniapp_auth_test_cases.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/docs/miniapp_login_frontend_example.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/docs/miniapp_login_implementation_guide.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/docs/miniapp_login_page_example.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/docs/miniapp_login_page_example.wxml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/docs/miniapp_login_utils_simple.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/docs/miniapp_phone_decrypt_guide.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/docs/miniapp_wechat_login_implementation.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/docs/rich_text_image_handling.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/docs/rich_text_url_fix.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/docs/sensitive_word_guide.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/docs/wechat-activity-sync.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/docs/wechat_miniapp_setup_guide.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-admin/src/main/resources/application.yml" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-admin/src/main/resources/application.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-admin/target/classes/application-druid.yml" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-admin/target/classes/application-druid.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-admin/target/classes/application.yml" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-admin/target/classes/application.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-miniapp/REST_API_文档.md" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-miniapp/REST_API_文档.md" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-miniapp/src/main/java/com/ruoyi/miniapp/controller/MiniAppApiController.java" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-miniapp/src/main/java/com/ruoyi/miniapp/controller/MiniAppApiController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-miniapp/src/main/java/com/ruoyi/miniapp/controller/MiniEventRegistrationController.java" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-miniapp/src/main/java/com/ruoyi/miniapp/controller/MiniEventRegistrationController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-miniapp/src/main/java/com/ruoyi/miniapp/controller/MiniPageContentController.java" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-miniapp/src/main/java/com/ruoyi/miniapp/controller/MiniPageContentController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-miniapp/src/main/java/com/ruoyi/miniapp/controller/MiniTechStarController.java" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-miniapp/src/main/java/com/ruoyi/miniapp/controller/MiniTechStarController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-miniapp/src/main/java/com/ruoyi/miniapp/controller/MiniUserController.java" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-miniapp/src/main/java/com/ruoyi/miniapp/controller/MiniUserController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-miniapp/src/main/java/com/ruoyi/miniapp/controller/XiqingConsultationController.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-miniapp/src/main/java/com/ruoyi/miniapp/domain/MiniTechStar.java" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-miniapp/src/main/java/com/ruoyi/miniapp/domain/MiniTechStar.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-miniapp/src/main/java/com/ruoyi/miniapp/domain/XiqingConsultation.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-miniapp/src/main/java/com/ruoyi/miniapp/domain/dto/MiniappLoginResponse.java" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-miniapp/src/main/java/com/ruoyi/miniapp/domain/dto/MiniappLoginResponse.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-miniapp/src/main/java/com/ruoyi/miniapp/mapper/MiniTechStarMapper.java" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-miniapp/src/main/java/com/ruoyi/miniapp/mapper/MiniTechStarMapper.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-miniapp/src/main/java/com/ruoyi/miniapp/mapper/XiqingConsultationMapper.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-miniapp/src/main/java/com/ruoyi/miniapp/service/IMiniTechStarService.java" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-miniapp/src/main/java/com/ruoyi/miniapp/service/IMiniTechStarService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-miniapp/src/main/java/com/ruoyi/miniapp/service/IXiqingConsultationService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-miniapp/src/main/java/com/ruoyi/miniapp/service/impl/MiniTechStarServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-miniapp/src/main/java/com/ruoyi/miniapp/service/impl/MiniTechStarServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-miniapp/src/main/java/com/ruoyi/miniapp/service/impl/XiqingConsultationServiceImpl.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-miniapp/src/main/resources/mapper/miniapp/MiniTechStarMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-miniapp/src/main/resources/mapper/miniapp/MiniTechStarMapper.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-miniapp/src/main/resources/mapper/miniapp/XiqingConsultationMapper.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-miniapp/target/classes/mapper/miniapp/MiniDemandCategoryMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-miniapp/target/classes/mapper/miniapp/MiniDemandCategoryMapper.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-miniapp/target/classes/mapper/miniapp/MiniDemandDockingMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-miniapp/target/classes/mapper/miniapp/MiniDemandDockingMapper.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-miniapp/target/classes/mapper/miniapp/MiniDemandMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-miniapp/target/classes/mapper/miniapp/MiniDemandMapper.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-miniapp/target/classes/mapper/miniapp/MiniPageContentMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-miniapp/target/classes/mapper/miniapp/MiniPageContentMapper.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-miniapp/target/classes/mapper/miniapp/MiniParkMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-miniapp/target/classes/mapper/miniapp/MiniParkMapper.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-miniapp/target/classes/mapper/miniapp/MiniTechStarMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-miniapp/target/classes/mapper/miniapp/MiniTechStarMapper.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-miniapp/target/classes/mapper/miniapp/MiniUserFollowMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-miniapp/target/classes/mapper/miniapp/MiniUserFollowMapper.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-miniapp/target/classes/mapper/miniapp/XiqingConsultationMapper.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-miniapp/新闻中心模块修改记录.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-system/src/main/java/com/ruoyi/system/service/impl/SysUserServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-system/src/main/java/com/ruoyi/system/service/impl/SysUserServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-system/src/main/resources/mapper/system/SysUserMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-system/src/main/resources/mapper/system/SysUserMapper.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-system/target/classes/mapper/system/SysUserMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-system/target/classes/mapper/system/SysUserMapper.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-ui/src/api/miniapp/techstar.js" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-ui/src/api/miniapp/techstar.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-ui/src/api/miniapp/xiqing/consultation.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-ui/src/views/index.vue" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-ui/src/views/index.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-ui/src/views/index_v1.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-ui/src/views/miniapp/business/techstar/index.vue" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-ui/src/views/miniapp/business/techstar/index.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-ui/src/views/miniapp/xiqing/activity-config/index.vue" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-ui/src/views/miniapp/xiqing/activity-config/index.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-ui/src/views/miniapp/xiqing/consultation/index.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-ui/src/views/miniapp/xiqing/registration-manage/index.vue" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-ui/src/views/miniapp/xiqing/registration-manage/index.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-ui/vue.config.js" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-ui/vue.config.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/sql/fix_mini_news_center_charset.sql" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/sql/migration_remove_demand_status.sql" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/sql/mini_notice_enhancement_completed.sql" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/sql/quartz.sql" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/sql/ry_20250522.sql" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/sql/sensitive_word_tables.sql" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/sql/tjht.sql" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/sql/update_mini_activity_wechat_fields.sql" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/sql/update_mini_notice_table.sql" beforeDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
    <option name="RESET_MODE" value="SOFT" />
  </component>
  <component name="KubernetesApiPersistence">{}</component>
  <component name="KubernetesApiProvider">{
  &quot;isMigrated&quot;: true
}</component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="D:/maven/maven/apache-maven-3.9.6" />
        <option name="localRepository" value="D:\maven\maven\mvn-repository" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="D:\maven\maven\apache-maven-3.9.6\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="CurrentFile" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;customColor&quot;: &quot;&quot;,
  &quot;associatedIndex&quot;: 2
}</component>
  <component name="ProjectId" id="2zInxgJ5QRhFOLKqgYyg52o7LSc" />
  <component name="ProjectViewState">
    <option name="showExcludedFiles" value="false" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Maven.ruoyi [clean].executor": "Run",
    "Maven.ruoyi [install].executor": "Run",
    "Maven.tjuhaitang_miniapp [clean].executor": "Run",
    "Maven.tjuhaitang_miniapp [compile].executor": "Run",
    "Maven.tjuhaitang_miniapp [install].executor": "Run",
    "Maven.tjuhaitang_miniapp [org.apache.maven.plugins:maven-clean-plugin:3.2.0:clean].executor": "Run",
    "Maven.tjuhaitang_miniapp [package].executor": "Run",
    "Maven.tjuhaitang_miniapp [validate].executor": "Run",
    "RequestMappingsPanelOrder0": "0",
    "RequestMappingsPanelOrder1": "1",
    "RequestMappingsPanelWidth0": "75",
    "RequestMappingsPanelWidth1": "75",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "SHARE_PROJECT_CONFIGURATION_FILES": "true",
    "Spring Boot.RuoYiApplication.executor": "Debug",
    "git-widget-placeholder": "master",
    "kotlin-language-version-configured": "true",
    "last_opened_file_path": "C:/Users/<USER>/Desktop/项目记录（吴龙龙）/tjuhaitang_miniapp",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "project.structure.last.edited": "Project",
    "project.structure.proportion": "0.0",
    "project.structure.side.proportion": "0.0",
    "settings.editor.selected.configurable": "preferences.pluginManager",
    "ts.external.directory.path": "D:\\IntelliJ IDEA 2024.2.3\\plugins\\javascript-plugin\\jsLanguageServicesImpl\\external",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\Desktop\项目记录（吴龙龙）\tjuhaitang_miniapp" />
      <recent name="C:\Users\<USER>\Desktop\项目记录（吴龙龙）\tjuhaitang_miniapp\ruoyi-ui\src\assets\icons\svg" />
    </key>
    <key name="MoveClassesOrPackagesDialog.RECENTS_KEY">
      <recent name="com.ruoyi.miniapp.controller" />
    </key>
  </component>
  <component name="RunManager">
    <configuration default="true" type="JetRunConfigurationType">
      <module name="RuoYi-Vue" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="RuoYiApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="FRAME_DEACTIVATION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <module name="ruoyi-admin" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ruoyi.RuoYiApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-28b599e66164-intellij.indexing.shared.core-IU-242.23339.11" />
        <option value="bundled-js-predefined-d6986cc7102b-5c90d61e3bab-JavaScript-IU-242.23339.11" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="76b702cc-2850-472c-9039-935eb1ffa66c" name="Changes" comment="" />
      <created>1751427704689</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1751427704689</updated>
      <workItem from="1751427708659" duration="47875000" />
      <workItem from="1751855410839" duration="795000" />
      <workItem from="1751856240516" duration="21000" />
      <workItem from="1751856270005" duration="37696000" />
      <workItem from="1751960768436" duration="415000" />
      <workItem from="1751961217500" duration="63000" />
      <workItem from="1751961297784" duration="142000" />
      <workItem from="1751961476241" duration="77000" />
      <workItem from="1751961563445" duration="581000" />
      <workItem from="1751962169130" duration="8266000" />
      <workItem from="1753236810992" duration="6595000" />
      <workItem from="1753251007652" duration="980000" />
      <workItem from="1753252072087" duration="304000" />
      <workItem from="1753252909597" duration="1915000" />
      <workItem from="1753254873747" duration="8971000" />
      <workItem from="1753318455301" duration="227000" />
      <workItem from="1753318693312" duration="50000" />
      <workItem from="1753318753418" duration="2392000" />
      <workItem from="1753321403398" duration="47735000" />
      <workItem from="1753511953529" duration="26000" />
      <workItem from="1753514064069" duration="31000" />
      <workItem from="1753514107251" duration="32000" />
      <workItem from="1753514147012" duration="48000" />
      <workItem from="1753514284407" duration="52000" />
      <workItem from="1753514343458" duration="999000" />
      <workItem from="1753515506174" duration="30000" />
      <workItem from="1753515545997" duration="67000" />
      <workItem from="1753515621179" duration="135000" />
      <workItem from="1753515780413" duration="848000" />
      <workItem from="1753592983987" duration="92710000" />
    </task>
    <task id="LOCAL-00001" summary="项目构建">
      <option name="closed" value="true" />
      <created>1751857168179</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1751857168179</updated>
    </task>
    <task id="LOCAL-00002" summary="需求广场over">
      <option name="closed" value="true" />
      <created>1751874137094</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1751874137094</updated>
    </task>
    <task id="LOCAL-00003" summary="敏感词过滤">
      <option name="closed" value="true" />
      <created>1753086453529</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1753086453529</updated>
    </task>
    <task id="LOCAL-00004" summary="敏感词过滤">
      <option name="closed" value="true" />
      <created>1753173023916</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1753173023916</updated>
    </task>
    <task id="LOCAL-00005" summary="微信参数配置做了些修改">
      <option name="closed" value="true" />
      <created>1753239491754</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1753239491754</updated>
    </task>
    <task id="LOCAL-00006" summary="演示版">
      <option name="closed" value="true" />
      <created>1753436126588</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1753436126588</updated>
    </task>
    <task id="LOCAL-00007" summary="揭榜，关注">
      <option name="closed" value="true" />
      <created>1753664658029</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1753664658029</updated>
    </task>
    <task id="LOCAL-00008" summary="联系方式管理">
      <option name="closed" value="true" />
      <created>1753759306579</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1753759306579</updated>
    </task>
    <task id="LOCAL-00009" summary="标识不能修改">
      <option name="closed" value="true" />
      <created>1753779407411</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1753779407411</updated>
    </task>
    <task id="LOCAL-00010" summary="fix:修复微信登录解密手机号失败等问题">
      <option name="closed" value="true" />
      <created>1753932673725</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1753932673725</updated>
    </task>
    <task id="LOCAL-00011" summary="fix:修复微信登录解密手机号失败等问题">
      <option name="closed" value="true" />
      <created>1753932693297</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1753932693297</updated>
    </task>
    <task id="LOCAL-00012" summary="fix:修复微信登录解密手机号失败等问题">
      <option name="closed" value="true" />
      <created>1753932775040</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1753932775040</updated>
    </task>
    <task id="LOCAL-00013" summary="fix:获取到DEFAULT的联系人">
      <option name="closed" value="true" />
      <created>1753933049312</created>
      <option name="number" value="00013" />
      <option name="presentableId" value="LOCAL-00013" />
      <option name="project" value="LOCAL" />
      <updated>1753933049312</updated>
    </task>
    <option name="localTasksCounter" value="14" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="项目构建" />
    <MESSAGE value="需求广场over" />
    <MESSAGE value="敏感词过滤" />
    <MESSAGE value="微信参数配置做了些修改" />
    <MESSAGE value="演示版" />
    <MESSAGE value="揭榜，关注" />
    <MESSAGE value="联系方式管理" />
    <MESSAGE value="标识不能修改" />
    <MESSAGE value="fix:修复微信登录解密手机号失败等问题" />
    <MESSAGE value="fix:获取到DEFAULT的联系人" />
    <option name="LAST_COMMIT_MESSAGE" value="fix:获取到DEFAULT的联系人" />
  </component>
  <component name="XDebuggerManager">
    <watches-manager>
      <configuration name="SpringBootApplicationConfigurationType">
        <watch expression="" />
      </configuration>
    </watches-manager>
  </component>
</project>